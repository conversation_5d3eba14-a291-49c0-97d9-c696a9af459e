package dtos

import (
	"dexmarket-ws/internal/domain/preset"
)

// CreatePresetInput represents input data for creating a preset
type CreatePresetInput struct {
	UserID int64 `json:"userId" binding:"required"`
}

type UpdatedPresetDto struct {
	UserID      int64   `json:"userId"`
	ID          int64   `json:"id"`
	Name        string  `json:"name"`
	SettingType string  `json:"settingType"`
	Slippage    float64 `json:"slippage"`
	Priority    float64 `json:"priority"`
	Bribe       float64 `json:"bribe"`
	IsAutoFee   bool    `json:"isAutoFee"`
	MaxFee      int64   `json:"maxFee"`
	MEVMode     string  `json:"mevMode"`
	RPC         string  `json:"rpc"`
	CreatedAt   int64   `json:"createdAt"`
}

// PresetDTO represents the preset data transfer object
type PresetDTO struct {
	UserID      int64   `json:"userId"`
	ID          int64   `json:"id"`
	Name        string  `json:"name"`
	SettingType string  `json:"settingType"`
	Slippage    float64 `json:"slippage"`
	Priority    float64 `json:"priority"`
	Bribe       float64 `json:"bribe"`
	IsAutoFee   bool    `json:"isAutoFee"`
	MaxFee      int64   `json:"maxFee"`
	MEVMode     string  `json:"mevMode"`
	RPC         string  `json:"rpc"`
	CreatedAt   int64   `json:"createdAt"`
	UpdatedAt   int64   `json:"updatedAt"`
}

// PresetResponseDTO represents the preset response data
type PresetResponseDTO struct {
	Presets []PresetDTO `json:"presets"`
}

// MapPresetToPresetDTOs converts domain preset models to DTOs
func MapPresetToPresetDTOs(presets []preset.Preset) []PresetDTO {
	presetDTOs := make([]PresetDTO, len(presets))
	for i, w := range presets {
		presetDTOs[i] = MapPresetToDTO(w)
	}
	return presetDTOs
}

// MapPresetToDTO converts a single domain wallet model to DTO
func MapPresetToDTO(p preset.Preset) PresetDTO {
	return PresetDTO{
		ID:          p.ID,
		Name:        p.Name,
		SettingType: p.SettingType,
		Slippage:    p.Slippage,
		Priority:    p.Priority,
		Bribe:       p.Bribe,
		IsAutoFee:   p.IsAutoFee,
		MaxFee:      p.MaxFee,
		MEVMode:     p.MEVMode,
		RPC:         p.RPC,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}
}
