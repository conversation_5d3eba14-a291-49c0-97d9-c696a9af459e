package postgres

import (
	"context"
	"dexmarket-ws/internal/database"
	domainPreset "dexmarket-ws/internal/domain/preset"
	"errors"
	"log"
	"time"
)

// presetRepository implements the preset.Repository interface
type presetRepository struct {
	db *database.Queries
}

// NewPresetRepository creates a new preset repository
func NewPresetRepository(db *database.Queries) domainPreset.PresetRepo {
	return &presetRepository{
		db: db,
	}
}

// FindByUserID finds presets by user ID
func (r *presetRepository) FindByUserID(ctx context.Context, userID int64) ([]domainPreset.Preset, error) {
	presets, err := r.db.FindPresetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	result := make([]domainPreset.Preset, len(presets))
	for i, p := range presets {
		result[i] = domainPreset.ToPreset(p)
	}
	return result, nil
}

// Save saves a preset
func (r *presetRepository) CreatePreset(ctx context.Context, createPresetDto *domainPreset.Preset) ([]domainPreset.Preset, error) {
	presetParams := database.CreatePresetParams{
		UserID:      createPresetDto.UserID,
		Name:        createPresetDto.Name,
		SettingType: createPresetDto.SettingType,
		Slippage:    createPresetDto.Slippage,
		Priority:    createPresetDto.Priority,
		Bribe:       createPresetDto.Bribe,
		IsAutoFee:   createPresetDto.IsAutoFee,
		MaxFee:      createPresetDto.MaxFee,
		MEVMode:     createPresetDto.MEVMode,
		RPC:         createPresetDto.RPC,
		CreatedAt:   time.Now().Unix(),
		UpdatedAt:   time.Now().Unix(),
	}
	presets, err := r.db.CreatePreset(ctx, presetParams)
	if err != nil {
		log.Printf("[ERROR] %v", err)
		return nil, errors.New("error creating PresetRepo")
	}
	var result []domainPreset.Preset
	for _, p := range presets {
		result = append(result, domainPreset.ToPreset(p))
	}
	return result, nil
}

func (r *presetRepository) UpdatePreset(ctx context.Context, preset *domainPreset.Preset) (domainPreset.Preset, error) {
	presetParams := database.UpdatePresetNameParams{
		ID:        preset.ID,
		Name:      preset.Name,
		UpdatedAt: time.Now().Unix(),
	}

	updatedPreset, err := r.db.UpdateWalletName(ctx, presetParams)
	if err != nil {
		return domainPreset.Preset{}, err
	}
	result := domainPreset.ToPreset(updatedPreset)
	return result, nil
}
