-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS DEXMARKET_WS.USER_PRESET
(
    ID              BIGINT GENERATED ALWAYS AS IDENTITY NOT NULL UNIQUE,
    USER_ID         BIGINT NOT NULL,
    NAME            VARCHAR(255) NOT NULL DEFAULT 'preset',
    SETTING_TYPE    VARCHAR(50) NOT NULL DEFAULT 'buy',
    SL<PERSON>PAGE        BIGINT NOT NULL DEFAULT 20,
    PRIORITY        BIGINT NOT NULL DEFAULT 0.001,
    BRIBE           BIGINT NOT NULL DEFAULT 0.001,
    IS_AUTO_FEE     BOOLEAN NOT NULL DEFAULT true,
    MAX_FEE         BIGINT NOT NULL DEFAULT 1000000,
    MEV_MODE        VARCHAR(50) NOT NULL DEFAULT 'off',
    R<PERSON>             VARCHAR(255) NOT NULL DEFAULT '',
    CREATED_AT      BIGINT NOT NULL,
    UPDATED_AT      BIGINT NOT NULL,

    CONSTRAINT PK_USER_PRESET PRIMARY KEY (ID),
    CONSTRAINT FK_USER_PRESET_USER_ID FOREIGN KEY (USER_ID) REFERENCES DEXMARKET_WS.USERS(ID)
    );
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS DEXMARKET_WS.USER_PRESET;
-- +goose StatementEnd
